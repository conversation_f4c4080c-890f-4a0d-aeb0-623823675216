import { useState } from "react";
import LoadingSpinner from "./LoadingSpinner";
import Button from "./Button";

/**
 * Demo component to showcase the enhanced loading UI variants
 * This can be used for testing and demonstration purposes
 */
export default function LoadingDemo() {
  const [showFullScreen, setShowFullScreen] = useState(false);
  const [variant, setVariant] = useState<"default" | "minimal" | "enhanced">("enhanced");
  const [size, setSize] = useState<"sm" | "md" | "lg">("md");

  return (
    <div className="p-8 bg-body-bg min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-white text-3xl font-bold mb-8">Loading UI Demo</h1>
        
        {/* Controls */}
        <div className="bg-main-bg rounded-2xl p-6 mb-8 border border-white/10">
          <h2 className="text-white text-xl font-semibold mb-4">Controls</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Variant Selection */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">Variant</label>
              <select 
                value={variant} 
                onChange={(e) => setVariant(e.target.value as any)}
                className="w-full bg-neutral-800 text-white rounded-lg px-3 py-2 border border-neutral-600"
              >
                <option value="default">Default</option>
                <option value="minimal">Minimal</option>
                <option value="enhanced">Enhanced</option>
              </select>
            </div>
            
            {/* Size Selection */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">Size</label>
              <select 
                value={size} 
                onChange={(e) => setSize(e.target.value as any)}
                className="w-full bg-neutral-800 text-white rounded-lg px-3 py-2 border border-neutral-600"
              >
                <option value="sm">Small</option>
                <option value="md">Medium</option>
                <option value="lg">Large</option>
              </select>
            </div>
            
            {/* Full Screen Toggle */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">Full Screen</label>
              <Button
                variant={showFullScreen ? "primary" : "outline"}
                onClick={() => setShowFullScreen(!showFullScreen)}
                className="w-full"
              >
                {showFullScreen ? "Hide" : "Show"} Full Screen
              </Button>
            </div>
          </div>
        </div>

        {/* Inline Demos */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-main-bg rounded-2xl p-6 border border-white/10">
            <h3 className="text-white text-lg font-semibold mb-4">Default Variant</h3>
            <LoadingSpinner variant="default" size={size} text="Loading content..." />
          </div>
          
          <div className="bg-main-bg rounded-2xl p-6 border border-white/10">
            <h3 className="text-white text-lg font-semibold mb-4">Minimal Variant</h3>
            <LoadingSpinner variant="minimal" size={size} text="Loading..." />
          </div>
          
          <div className="bg-main-bg rounded-2xl p-6 border border-white/10">
            <h3 className="text-white text-lg font-semibold mb-4">Enhanced Variant</h3>
            <LoadingSpinner variant="enhanced" size={size} text="Processing..." />
          </div>
        </div>

        {/* Navigation Loading Simulation */}
        <div className="bg-main-bg rounded-2xl p-6 border border-white/10">
          <h3 className="text-white text-lg font-semibold mb-4">Navigation Loading Simulation</h3>
          <p className="text-neutral-400 mb-4">
            This simulates the global loading overlay that appears during navigation.
          </p>
          <Button
            variant="primary"
            onClick={() => {
              // Simulate navigation loading
              const overlay = document.createElement('div');
              overlay.className = 'fixed inset-0 bg-body-bg/95 backdrop-blur-sm flex items-center justify-center z-[9999] pointer-events-none animate-fadeIn';
              overlay.innerHTML = `
                <div class="bg-main-bg/95 backdrop-blur-md rounded-2xl p-8 flex flex-col items-center border border-white/10 shadow-2xl animate-scaleIn">
                  <div class="relative flex items-center justify-center mb-6">
                    <div class="absolute animate-spin rounded-full h-16 w-16 border-2 border-transparent border-t-brand-red border-r-brand-red/50"></div>
                    <div class="absolute animate-spin rounded-full h-12 w-12 border-2 border-transparent border-t-brand-red/70 border-l-brand-red/30" style="animation-direction: reverse; animation-duration: 1.5s;"></div>
                    <div class="absolute animate-spin rounded-full h-8 w-8 border-2 border-transparent border-t-brand-red/50"></div>
                    <div class="w-3 h-3 bg-brand-red rounded-full animate-pulse"></div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-white text-lg font-medium">Loading</span>
                    <div class="flex space-x-1">
                      <div class="w-1 h-1 bg-brand-red rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                      <div class="w-1 h-1 bg-brand-red rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                      <div class="w-1 h-1 bg-brand-red rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
                    </div>
                  </div>
                  <div class="mt-4 w-32 h-1 bg-white/10 rounded-full overflow-hidden">
                    <div class="h-full bg-gradient-to-r from-brand-red to-red-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              `;
              
              document.body.appendChild(overlay);
              
              // Remove after 3 seconds
              setTimeout(() => {
                document.body.removeChild(overlay);
              }, 3000);
            }}
          >
            Simulate Navigation Loading
          </Button>
        </div>

        {/* Full Screen Demo */}
        {showFullScreen && (
          <LoadingSpinner 
            fullScreen 
            variant={variant} 
            size={size} 
            text="Full screen loading demo..."
          />
        )}
      </div>
    </div>
  );
}
