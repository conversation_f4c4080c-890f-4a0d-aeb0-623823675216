interface LoadingSpinnerProps {
  text?: string;
  size?: number;
  className?: string;
  fullScreen?: boolean;
}

export default function LoadingSpinner({
  text,
  size = 32,
  className = "",
  fullScreen = false,
}: LoadingSpinnerProps) {
  const containerClass = fullScreen
    ? "fixed inset-0 flex items-center justify-center bg-black/80 z-50"
    : "flex flex-col items-center justify-center p-4";

  return (
    <div className={`${containerClass} ${className}`}>
      <div className="text-center">
        <div
          className="animate-spin rounded-full border-2 border-transparent border-t-brand-red mx-auto"
          style={{ width: size, height: size }}
        />
        {text && <p className="mt-4 text-white text-sm md:text-lg">{text}</p>}
      </div>
    </div>
  );
}
