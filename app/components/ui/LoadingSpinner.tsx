interface LoadingSpinnerProps {
  text?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
  fullScreen?: boolean;
  variant?: "default" | "minimal" | "enhanced";
}

export default function LoadingSpinner({
  text,
  size = "md",
  className = "",
  fullScreen = false,
  variant = "default",
}: LoadingSpinnerProps) {
  const containerClass = fullScreen
    ? "fixed inset-0 flex items-center justify-center bg-body-bg/95 backdrop-blur-sm z-50 animate-fadeIn"
    : "flex flex-col items-center justify-center p-4";

  // Size configurations
  const sizeConfig = {
    sm: { spinner: "h-6 w-6", text: "text-sm" },
    md: { spinner: "h-8 w-8 md:h-12 md:w-12", text: "text-sm md:text-lg" },
    lg: { spinner: "h-12 w-12 md:h-16 md:w-16", text: "text-lg md:text-xl" },
  };

  const currentSize = sizeConfig[size];

  // Render different variants
  const renderSpinner = () => {
    if (variant === "enhanced") {
      return (
        <div className="relative flex items-center justify-center mb-4">
          {/* Outer ring */}
          <div
            className={`absolute animate-spin rounded-full border-2 border-transparent border-t-brand-red border-r-brand-red/50 ${currentSize.spinner}`}
          ></div>
          {/* Inner ring */}
          <div
            className={`absolute animate-spin rounded-full border-2 border-transparent border-t-brand-red/70 border-l-brand-red/30`}
            style={{
              animationDirection: "reverse",
              animationDuration: "1.5s",
              width: size === "lg" ? "2rem" : size === "md" ? "1.5rem" : "1rem",
              height:
                size === "lg" ? "2rem" : size === "md" ? "1.5rem" : "1rem",
            }}
          ></div>
          {/* Center dot */}
          <div className="w-2 h-2 bg-brand-red rounded-full animate-pulse"></div>
        </div>
      );
    }

    if (variant === "minimal") {
      return (
        <div
          className={`animate-spin rounded-full border-2 border-transparent border-t-brand-red ${currentSize.spinner} mx-auto`}
        ></div>
      );
    }

    // Default variant
    return (
      <div className="relative flex items-center justify-center mb-4">
        <div
          className={`animate-spin rounded-full border-2 border-transparent border-t-brand-red border-r-brand-red/50 ${currentSize.spinner}`}
        ></div>
        <div className="absolute w-2 h-2 bg-brand-red rounded-full animate-pulse"></div>
      </div>
    );
  };

  return (
    <div className={`${containerClass} ${className}`}>
      <div
        className={`text-center ${
          fullScreen
            ? "bg-main-bg/95 backdrop-blur-md rounded-2xl p-8 border border-white/10 shadow-2xl animate-scaleIn"
            : ""
        }`}
      >
        {renderSpinner()}

        {text && (
          <div className="flex items-center justify-center space-x-2">
            <span className={`text-white font-medium ${currentSize.text}`}>
              {text}
            </span>
            {variant !== "minimal" && (
              <div className="flex space-x-1">
                <div
                  className="w-1 h-1 bg-brand-red rounded-full animate-bounce"
                  style={{ animationDelay: "0ms" }}
                ></div>
                <div
                  className="w-1 h-1 bg-brand-red rounded-full animate-bounce"
                  style={{ animationDelay: "150ms" }}
                ></div>
                <div
                  className="w-1 h-1 bg-brand-red rounded-full animate-bounce"
                  style={{ animationDelay: "300ms" }}
                ></div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
