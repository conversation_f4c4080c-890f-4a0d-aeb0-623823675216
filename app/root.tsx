import {
  <PERSON>s,
  <PERSON>a,
  Outlet,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLoaderData,
  useRouteError,
  isRouteErrorResponse,
  useRouteLoaderData,
  useNavigation,
} from "@remix-run/react";
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import { useTranslation } from "react-i18next";
import { useEffect, startTransition } from "react";

import i18nextClient from "i18next";
import { useLanguageChangeRevalidation } from "~/hooks/useLanguageChangeRevalidation";

import LoadingSpinner from "~/components/ui/LoadingSpinner";
import { ToastProviderComponent } from "~/components/ui/toast-provider";
import styles from "./tailwind.css?url";
import resetStyles from "./reset.css?url";
import "@radix-ui/themes/styles.css";
import splideStyles from "@splidejs/react-splide/css?url";
import splideCustomStyles from "~/styles/splide-custom.css?url";
import i18next from "./i18next.server";
import { AuthProvider } from "~/context/auth-context";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useChannelTracking } from "~/hooks/useChannelTracking";

export const links: LinksFunction = () => [
  // title
  { title: "SnapDrama", page: "/" },
  { rel: "icon", type: "image/x-icon", href: "/favicon.ico" },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
  { rel: "stylesheet", href: styles },
  { rel: "stylesheet", href: resetStyles },

  { rel: "stylesheet", href: splideStyles },
  { rel: "stylesheet", href: splideCustomStyles },
];
export async function loader({ request }: LoaderFunctionArgs) {
  let locale = await i18next.getLocale(request);
  return { locale };
}

export async function clientLoader({ request }: LoaderFunctionArgs) {
  // Try to get language from headers, otherwise use default
  const locale = i18nextClient.language;
  // add locale to axios config
  console.log("clientLoader locale", locale);
  return { locale };
}

// Document component to maintain consistent HTML structure
function Document({
  children,
}: {
  children: React.ReactNode;

  title?: string;
}) {
  let loaderData = useLoaderData<typeof loader>();
  const locale = loaderData?.locale || "en";
  return (
    <html lang={locale} dir={locale === "ar" ? "rtl" : "ltr"}>
      <head>
        {/* <title>{title}</title> */}
        <meta charSet="utf-8" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, viewport-fit=cover"
        />
        <Meta />
        <Links />
      </head>
      <body className="bg-body-bg">
        {children}
        <ScrollRestoration />
        <Scripts />

        {/* Google Analytics */}
        <script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-BSBPM9K0VF"
        ></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-BSBPM9K0VF');
            `,
          }}
        ></script>
      </body>
    </html>
  );
}

export function Layout({ children }: { children: React.ReactNode }) {
  const loaderData = useRouteLoaderData<typeof clientLoader>("root");
  const locale = loaderData?.locale;

  return <Document>{children}</Document>;
}

const queryClient = new QueryClient();
export default function App() {
  const navigation = useNavigation();
  const isLoading =
    navigation.state === "loading" || navigation.state === "submitting";

  useLanguageChangeRevalidation();
  useChannelTracking(); // Track channel visits on route changes

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {/* <NavigationLoadingProvider> */}
        <ToastProviderComponent>
          {/* Simple Global loading overlay */}
          {isLoading && (
            <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-[9999] pointer-events-none">
              <div className="animate-spin rounded-full h-12 w-12 border-2 border-transparent border-t-brand-red"></div>
            </div>
          )}

          {/* Simple Loading bar at top */}
          {isLoading && (
            <div className="fixed top-0 left-0 right-0 h-1 bg-brand-red z-[9998]"></div>
          )}

          <Outlet />
        </ToastProviderComponent>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export function ErrorBoundary() {
  const error = useRouteError();
  const { t } = useTranslation();
  const loaderData = useRouteLoaderData<typeof clientLoader>("root");
  const locale = loaderData?.locale || "en";

  return (
    <Document title="Error - SnapDrama">
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">
            {isRouteErrorResponse(error) ? error.status : "500"}
          </h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">
            {isRouteErrorResponse(error)
              ? error.statusText
              : t("error.internalServerError")}
          </h2>
          <p className="text-gray-600 mb-8">
            {isRouteErrorResponse(error)
              ? error.data?.message
              : t("error.somethingWentWrong")}
          </p>
          <a
            href="/"
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t("error.backToHome")}
          </a>
        </div>
      </div>
    </Document>
  );
}

export function HydrateFallback() {
  return (
    <>
      <LoadingSpinner fullScreen />

      <Scripts />
    </>
  );
}
