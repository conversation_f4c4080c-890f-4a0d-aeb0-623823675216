import {
  <PERSON>s,
  <PERSON>a,
  Outlet,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLoaderData,
  useRouteError,
  isRouteErrorResponse,
  useRouteLoaderData,
  useNavigation,
} from "@remix-run/react";
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import { useTranslation } from "react-i18next";
import { useEffect, startTransition } from "react";

import i18nextClient from "i18next";
import { useLanguageChangeRevalidation } from "~/hooks/useLanguageChangeRevalidation";

import LoadingSpinner from "~/components/ui/LoadingSpinner";
import { ToastProviderComponent } from "~/components/ui/toast-provider";
import styles from "./tailwind.css?url";
import resetStyles from "./reset.css?url";
import "@radix-ui/themes/styles.css";
import splideStyles from "@splidejs/react-splide/css?url";
import splideCustomStyles from "~/styles/splide-custom.css?url";
import i18next from "./i18next.server";
import { AuthProvider } from "~/context/auth-context";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useChannelTracking } from "~/hooks/useChannelTracking";

export const links: LinksFunction = () => [
  // title
  { title: "SnapDrama", page: "/" },
  { rel: "icon", type: "image/x-icon", href: "/favicon.ico" },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
  { rel: "stylesheet", href: styles },
  { rel: "stylesheet", href: resetStyles },

  { rel: "stylesheet", href: splideStyles },
  { rel: "stylesheet", href: splideCustomStyles },
];
export async function loader({ request }: LoaderFunctionArgs) {
  let locale = await i18next.getLocale(request);
  return { locale };
}

export async function clientLoader({ request }: LoaderFunctionArgs) {
  // Try to get language from headers, otherwise use default
  const locale = i18nextClient.language;
  // add locale to axios config
  console.log("clientLoader locale", locale);
  return { locale };
}

// Document component to maintain consistent HTML structure
function Document({
  children,
}: {
  children: React.ReactNode;

  title?: string;
}) {
  let loaderData = useLoaderData<typeof loader>();
  const locale = loaderData?.locale || "en";
  return (
    <html lang={locale} dir={locale === "ar" ? "rtl" : "ltr"}>
      <head>
        {/* <title>{title}</title> */}
        <meta charSet="utf-8" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, viewport-fit=cover"
        />
        <Meta />
        <Links />
      </head>
      <body className="bg-body-bg">
        {children}
        <ScrollRestoration />
        <Scripts />

        {/* Google Analytics */}
        <script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-BSBPM9K0VF"
        ></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-BSBPM9K0VF');
            `,
          }}
        ></script>
      </body>
    </html>
  );
}

export function Layout({ children }: { children: React.ReactNode }) {
  const loaderData = useRouteLoaderData<typeof clientLoader>("root");
  const locale = loaderData?.locale;

  return <Document>{children}</Document>;
}

const queryClient = new QueryClient();
export default function App() {
  const navigation = useNavigation();
  const isLoading =
    navigation.state === "loading" || navigation.state === "submitting";

  useLanguageChangeRevalidation();
  useChannelTracking(); // Track channel visits on route changes

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {/* <NavigationLoadingProvider> */}
        <ToastProviderComponent>
          {/* Enhanced Global loading overlay */}
          {isLoading && (
            <div className="fixed inset-0 bg-body-bg/95 backdrop-blur-sm flex items-center justify-center z-[9999] pointer-events-none animate-fadeIn">
              <div className="bg-main-bg/95 backdrop-blur-md rounded-2xl p-8 flex flex-col items-center border border-white/10 shadow-2xl animate-scaleIn">
                {/* Enhanced spinner with multiple rings */}
                <div className="relative flex items-center justify-center mb-6">
                  {/* Outer ring */}
                  <div className="absolute animate-spin rounded-full h-16 w-16 border-2 border-transparent border-t-brand-red border-r-brand-red/50"></div>
                  {/* Middle ring */}
                  <div
                    className="absolute animate-spin rounded-full h-12 w-12 border-2 border-transparent border-t-brand-red/70 border-l-brand-red/30"
                    style={{
                      animationDirection: "reverse",
                      animationDuration: "1.5s",
                    }}
                  ></div>
                  {/* Inner ring */}
                  <div className="absolute animate-spin rounded-full h-8 w-8 border-2 border-transparent border-t-brand-red/50"></div>
                  {/* Center dot */}
                  <div className="w-3 h-3 bg-brand-red rounded-full animate-pulse"></div>
                </div>

                {/* Loading text with typing animation */}
                <div className="flex items-center space-x-2">
                  <span className="text-white text-lg font-medium">
                    Loading
                  </span>
                  <div className="flex space-x-1">
                    <div
                      className="w-1 h-1 bg-brand-red rounded-full animate-bounce"
                      style={{ animationDelay: "0ms" }}
                    ></div>
                    <div
                      className="w-1 h-1 bg-brand-red rounded-full animate-bounce"
                      style={{ animationDelay: "150ms" }}
                    ></div>
                    <div
                      className="w-1 h-1 bg-brand-red rounded-full animate-bounce"
                      style={{ animationDelay: "300ms" }}
                    ></div>
                  </div>
                </div>

                {/* Subtle progress indicator */}
                <div className="mt-4 w-32 h-1 bg-white/10 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-brand-red to-red-400 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          )}

          {/* Enhanced Loading bar at top */}
          {isLoading && (
            <div className="fixed top-0 left-0 right-0 h-1 z-[9998] overflow-hidden">
              <div className="h-full bg-gradient-to-r from-transparent via-brand-red to-transparent animate-pulse"></div>
              <div
                className="absolute top-0 left-0 h-full w-full bg-gradient-to-r from-brand-red/50 to-transparent animate-pulse"
                style={{ animationDelay: "0.5s" }}
              ></div>
            </div>
          )}

          <Outlet />
        </ToastProviderComponent>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export function ErrorBoundary() {
  const error = useRouteError();
  const { t } = useTranslation();
  const loaderData = useRouteLoaderData<typeof clientLoader>("root");
  const locale = loaderData?.locale || "en";

  return (
    <Document title="Error - SnapDrama">
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">
            {isRouteErrorResponse(error) ? error.status : "500"}
          </h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">
            {isRouteErrorResponse(error)
              ? error.statusText
              : t("error.internalServerError")}
          </h2>
          <p className="text-gray-600 mb-8">
            {isRouteErrorResponse(error)
              ? error.data?.message
              : t("error.somethingWentWrong")}
          </p>
          <a
            href="/"
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t("error.backToHome")}
          </a>
        </div>
      </div>
    </Document>
  );
}

export function HydrateFallback() {
  return (
    <>
      <LoadingSpinner
        fullScreen
        variant="enhanced"
        size="lg"
        text="Initializing SnapDrama..."
      />

      <Scripts />
    </>
  );
}
